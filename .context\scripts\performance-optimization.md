# 性能优化脚本

## 前端性能优化

### React 性能优化脚本
```javascript
// React 性能监控和优化工具
const ReactPerformanceOptimizer = {
  // 组件渲染次数监控
  renderCounter: new Map(),
  
  // 监控组件渲染
  trackRender: (componentName) => {
    const count = ReactPerformanceOptimizer.renderCounter.get(componentName) || 0;
    ReactPerformanceOptimizer.renderCounter.set(componentName, count + 1);
    
    if (count > 10) {
      console.warn(`⚠️ ${componentName} 渲染次数过多: ${count + 1}`);
    }
  },

  // 检测不必要的重渲染
  detectUnnecessaryRenders: (Component) => {
    return React.memo(Component, (prevProps, nextProps) => {
      const changed = Object.keys(nextProps).some(key => 
        prevProps[key] !== nextProps[key]
      );
      
      if (!changed) {
        console.log(`🔄 ${Component.name} 避免了不必要的重渲染`);
      }
      
      return !changed;
    });
  },

  // 虚拟化长列表
  VirtualizedList: ({ items, itemHeight, containerHeight, renderItem }) => {
    const [scrollTop, setScrollTop] = React.useState(0);
    
    const visibleStart = Math.floor(scrollTop / itemHeight);
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    );
    
    const visibleItems = items.slice(visibleStart, visibleEnd + 1);
    
    return (
      <div 
        style={{ height: containerHeight, overflow: 'auto' }}
        onScroll={(e) => setScrollTop(e.target.scrollTop)}
      >
        <div style={{ height: items.length * itemHeight, position: 'relative' }}>
          {visibleItems.map((item, index) => (
            <div
              key={visibleStart + index}
              style={{
                position: 'absolute',
                top: (visibleStart + index) * itemHeight,
                height: itemHeight,
                width: '100%'
              }}
            >
              {renderItem(item, visibleStart + index)}
            </div>
          ))}
        </div>
      </div>
    );
  },

  // 图片懒加载
  LazyImage: ({ src, alt, placeholder, ...props }) => {
    const [imageSrc, setImageSrc] = React.useState(placeholder);
    const [imageRef, setImageRef] = React.useState();

    React.useEffect(() => {
      let observer;
      
      if (imageRef && imageSrc === placeholder) {
        observer = new IntersectionObserver(
          entries => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                setImageSrc(src);
                observer.unobserve(imageRef);
              }
            });
          },
          { threshold: 0.1 }
        );
        observer.observe(imageRef);
      }
      
      return () => {
        if (observer && observer.unobserve) {
          observer.unobserve(imageRef);
        }
      };
    }, [imageRef, imageSrc, placeholder, src]);

    return (
      <img
        {...props}
        ref={setImageRef}
        src={imageSrc}
        alt={alt}
      />
    );
  }
};

// 代码分割和懒加载
const LazyComponentLoader = {
  // 动态导入组件
  loadComponent: (importFunc) => {
    return React.lazy(() => 
      importFunc().then(module => ({
        default: module.default || module
      }))
    );
  },

  // 预加载组件
  preloadComponent: (importFunc) => {
    const componentImport = importFunc();
    return () => componentImport;
  },

  // 路由级别的代码分割
  createLazyRoute: (importFunc) => {
    const LazyComponent = React.lazy(importFunc);
    
    return (props) => (
      <React.Suspense fallback={<div>Loading...</div>}>
        <LazyComponent {...props} />
      </React.Suspense>
    );
  }
};

// Bundle 分析工具
const BundleAnalyzer = {
  // 分析包大小
  analyzeBundleSize: () => {
    if (process.env.NODE_ENV === 'development') {
      import('webpack-bundle-analyzer').then(({ BundleAnalyzerPlugin }) => {
        console.log('Bundle analysis available at http://localhost:8888');
      });
    }
  },

  // 检测重复依赖
  detectDuplicates: () => {
    const modules = new Map();
    
    if (window.__webpack_require__) {
      Object.keys(window.__webpack_require__.cache).forEach(moduleId => {
        const module = window.__webpack_require__.cache[moduleId];
        if (module && module.exports) {
          const moduleName = module.id || moduleId;
          if (modules.has(moduleName)) {
            console.warn(`🔄 检测到重复模块: ${moduleName}`);
          } else {
            modules.set(moduleName, module);
          }
        }
      });
    }
  }
};
```

### Vue 性能优化脚本
```javascript
// Vue 3 性能优化工具
const VuePerformanceOptimizer = {
  // 组件缓存
  createCachedComponent: (component, cacheKey) => {
    const cache = new Map();
    
    return {
      ...component,
      setup(props, context) {
        const key = typeof cacheKey === 'function' ? cacheKey(props) : cacheKey;
        
        if (cache.has(key)) {
          return cache.get(key);
        }
        
        const result = component.setup(props, context);
        cache.set(key, result);
        return result;
      }
    };
  },

  // 虚拟滚动
  VirtualScroll: {
    name: 'VirtualScroll',
    props: {
      items: Array,
      itemHeight: Number,
      containerHeight: Number
    },
    setup(props) {
      const scrollTop = ref(0);
      
      const visibleRange = computed(() => {
        const start = Math.floor(scrollTop.value / props.itemHeight);
        const end = Math.min(
          start + Math.ceil(props.containerHeight / props.itemHeight),
          props.items.length - 1
        );
        return { start, end };
      });
      
      const visibleItems = computed(() => {
        const { start, end } = visibleRange.value;
        return props.items.slice(start, end + 1).map((item, index) => ({
          item,
          index: start + index
        }));
      });
      
      return {
        scrollTop,
        visibleItems,
        visibleRange
      };
    },
    template: `
      <div 
        :style="{ height: containerHeight + 'px', overflow: 'auto' }"
        @scroll="scrollTop = $event.target.scrollTop"
      >
        <div :style="{ height: items.length * itemHeight + 'px', position: 'relative' }">
          <div
            v-for="{ item, index } in visibleItems"
            :key="index"
            :style="{
              position: 'absolute',
              top: index * itemHeight + 'px',
              height: itemHeight + 'px',
              width: '100%'
            }"
          >
            <slot :item="item" :index="index"></slot>
          </div>
        </div>
      </div>
    `
  },

  // 防抖和节流
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  throttle: (func, limit) => {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
};
```

## 后端性能优化

### Node.js 性能优化脚本
```javascript
// Node.js 性能监控和优化
const NodePerformanceOptimizer = {
  // 内存泄漏检测
  memoryLeakDetector: {
    start: () => {
      const initialMemory = process.memoryUsage();
      let checkCount = 0;
      
      const interval = setInterval(() => {
        checkCount++;
        const currentMemory = process.memoryUsage();
        const heapGrowth = currentMemory.heapUsed - initialMemory.heapUsed;
        
        if (heapGrowth > 50 * 1024 * 1024 && checkCount > 10) { // 50MB增长
          console.warn('⚠️ 可能存在内存泄漏:', {
            heapGrowth: Math.round(heapGrowth / 1024 / 1024) + 'MB',
            currentHeap: Math.round(currentMemory.heapUsed / 1024 / 1024) + 'MB'
          });
        }
        
        if (checkCount > 100) {
          clearInterval(interval);
        }
      }, 30000);
    }
  },

  // 事件循环监控
  eventLoopMonitor: {
    start: () => {
      const { performance } = require('perf_hooks');
      
      setInterval(() => {
        const start = performance.now();
        setImmediate(() => {
          const lag = performance.now() - start;
          if (lag > 10) {
            console.warn(`⚠️ 事件循环延迟: ${lag.toFixed(2)}ms`);
          }
        });
      }, 5000);
    }
  },

  // 缓存优化
  createCache: (maxSize = 1000, ttl = 300000) => {
    const cache = new Map();
    const timers = new Map();
    
    return {
      get: (key) => {
        if (cache.has(key)) {
          // 重置TTL
          if (timers.has(key)) {
            clearTimeout(timers.get(key));
          }
          timers.set(key, setTimeout(() => {
            cache.delete(key);
            timers.delete(key);
          }, ttl));
          
          return cache.get(key);
        }
        return null;
      },
      
      set: (key, value) => {
        // 检查缓存大小
        if (cache.size >= maxSize) {
          const firstKey = cache.keys().next().value;
          cache.delete(firstKey);
          if (timers.has(firstKey)) {
            clearTimeout(timers.get(firstKey));
            timers.delete(firstKey);
          }
        }
        
        cache.set(key, value);
        timers.set(key, setTimeout(() => {
          cache.delete(key);
          timers.delete(key);
        }, ttl));
      },
      
      clear: () => {
        cache.clear();
        timers.forEach(timer => clearTimeout(timer));
        timers.clear();
      },
      
      size: () => cache.size
    };
  },

  // 数据库连接池优化
  optimizeDbPool: (pool) => {
    // 监控连接池状态
    setInterval(() => {
      const stats = {
        total: pool.totalCount,
        idle: pool.idleCount,
        waiting: pool.waitingCount
      };
      
      if (stats.waiting > 5) {
        console.warn('⚠️ 数据库连接池等待过多:', stats);
      }
      
      if (stats.idle / stats.total < 0.2) {
        console.warn('⚠️ 数据库连接池空闲连接不足:', stats);
      }
    }, 30000);
    
    return pool;
  },

  // 请求限流
  createRateLimiter: (maxRequests = 100, windowMs = 60000) => {
    const requests = new Map();
    
    return (req, res, next) => {
      const key = req.ip || req.connection.remoteAddress;
      const now = Date.now();
      const windowStart = now - windowMs;
      
      // 清理过期记录
      if (requests.has(key)) {
        requests.set(key, requests.get(key).filter(time => time > windowStart));
      } else {
        requests.set(key, []);
      }
      
      const requestCount = requests.get(key).length;
      
      if (requestCount >= maxRequests) {
        return res.status(429).json({
          error: 'Too many requests',
          retryAfter: Math.ceil(windowMs / 1000)
        });
      }
      
      requests.get(key).push(now);
      next();
    };
  }
};

// Express 中间件优化
const ExpressOptimizations = {
  // 压缩中间件
  compression: require('compression')({
    filter: (req, res) => {
      if (req.headers['x-no-compression']) {
        return false;
      }
      return compression.filter(req, res);
    },
    threshold: 1024 // 只压缩大于1KB的响应
  }),

  // 静态文件缓存
  staticCache: (path, options = {}) => {
    return express.static(path, {
      maxAge: options.maxAge || '1d',
      etag: true,
      lastModified: true,
      setHeaders: (res, path) => {
        if (path.endsWith('.html')) {
          res.setHeader('Cache-Control', 'no-cache');
        }
      }
    });
  },

  // 请求日志优化
  optimizedLogger: (req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      
      // 只记录慢请求或错误请求
      if (duration > 1000 || res.statusCode >= 400) {
        console.log(`${req.method} ${req.url} - ${res.statusCode} (${duration}ms)`);
      }
    });
    
    next();
  }
};
```

## 数据库性能优化

### SQL 优化脚本
```sql
-- 索引优化分析
-- 查找缺失索引的查询
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE schemaname = 'public'
    AND n_distinct > 100
    AND correlation < 0.1;

-- 查找未使用的索引
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch,
    pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes 
WHERE idx_scan = 0
    AND schemaname = 'public'
ORDER BY pg_relation_size(indexrelid) DESC;

-- 查找重复索引
WITH index_columns AS (
    SELECT 
        i.indexrelid,
        i.indrelid,
        array_agg(a.attname ORDER BY a.attnum) as columns
    FROM pg_index i
    JOIN pg_attribute a ON a.attrelid = i.indrelid 
        AND a.attnum = ANY(i.indkey)
    WHERE i.indisvalid
    GROUP BY i.indexrelid, i.indrelid
)
SELECT 
    t.relname as table_name,
    array_agg(i.relname) as duplicate_indexes,
    ic.columns
FROM index_columns ic
JOIN pg_class t ON t.oid = ic.indrelid
JOIN pg_class i ON i.oid = ic.indexrelid
GROUP BY t.relname, ic.columns
HAVING count(*) > 1;

-- 查询优化建议
EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) 
SELECT * FROM users u
JOIN orders o ON u.id = o.user_id
WHERE u.created_at > '2023-01-01'
    AND o.status = 'completed';
```

### MongoDB 优化脚本
```javascript
// MongoDB 性能优化工具
const MongoOptimizer = {
  // 索引分析
  analyzeIndexes: async (db, collection) => {
    const stats = await db.collection(collection).stats();
    const indexes = await db.collection(collection).getIndexes();
    
    console.log(`📊 集合 ${collection} 统计:`, {
      文档数量: stats.count,
      平均文档大小: Math.round(stats.avgObjSize),
      总大小: Math.round(stats.size / 1024 / 1024) + 'MB',
      索引数量: indexes.length,
      索引大小: Math.round(stats.totalIndexSize / 1024 / 1024) + 'MB'
    });
    
    // 检查索引使用情况
    const indexStats = await db.collection(collection).aggregate([
      { $indexStats: {} }
    ]).toArray();
    
    indexStats.forEach(stat => {
      if (stat.accesses.ops === 0) {
        console.warn(`⚠️ 未使用的索引: ${stat.name}`);
      }
    });
  },

  // 查询优化
  optimizeQuery: (query) => {
    return {
      // 使用投影减少数据传输
      project: (fields) => {
        const projection = {};
        fields.forEach(field => projection[field] = 1);
        return { ...query, projection };
      },
      
      // 添加索引提示
      hint: (index) => {
        return { ...query, hint: index };
      },
      
      // 限制结果数量
      limit: (count) => {
        return { ...query, limit: count };
      },
      
      // 批量处理
      batch: async (collection, batchSize = 1000) => {
        const cursor = collection.find(query).batchSize(batchSize);
        const results = [];
        
        while (await cursor.hasNext()) {
          const batch = [];
          for (let i = 0; i < batchSize && await cursor.hasNext(); i++) {
            batch.push(await cursor.next());
          }
          results.push(batch);
        }
        
        return results;
      }
    };
  },

  // 聚合优化
  optimizeAggregation: (pipeline) => {
    const optimized = [...pipeline];
    
    // 将 $match 移到前面
    const matchStages = optimized.filter(stage => stage.$match);
    const otherStages = optimized.filter(stage => !stage.$match);
    
    return [...matchStages, ...otherStages];
  },

  // 连接池优化
  optimizeConnectionPool: (options = {}) => {
    return {
      maxPoolSize: options.maxPoolSize || 10,
      minPoolSize: options.minPoolSize || 2,
      maxIdleTimeMS: options.maxIdleTimeMS || 30000,
      waitQueueTimeoutMS: options.waitQueueTimeoutMS || 5000,
      serverSelectionTimeoutMS: options.serverSelectionTimeoutMS || 5000
    };
  }
};
```

## 缓存优化脚本

### Redis 缓存优化
```javascript
// Redis 缓存优化工具
const RedisCacheOptimizer = {
  // 智能缓存键生成
  generateCacheKey: (prefix, params) => {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {});
    
    const hash = require('crypto')
      .createHash('md5')
      .update(JSON.stringify(sortedParams))
      .digest('hex');
    
    return `${prefix}:${hash}`;
  },

  // 缓存装饰器
  cache: (redis, ttl = 3600) => {
    return (target, propertyName, descriptor) => {
      const method = descriptor.value;
      
      descriptor.value = async function(...args) {
        const cacheKey = RedisCacheOptimizer.generateCacheKey(
          `${target.constructor.name}:${propertyName}`,
          { args }
        );
        
        // 尝试从缓存获取
        const cached = await redis.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
        
        // 执行原方法
        const result = await method.apply(this, args);
        
        // 存储到缓存
        await redis.setex(cacheKey, ttl, JSON.stringify(result));
        
        return result;
      };
      
      return descriptor;
    };
  },

  // 缓存预热
  warmupCache: async (redis, warmupFunctions) => {
    console.log('🔥 开始缓存预热...');
    
    const promises = warmupFunctions.map(async (fn, index) => {
      try {
        await fn();
        console.log(`✅ 预热任务 ${index + 1} 完成`);
      } catch (error) {
        console.error(`❌ 预热任务 ${index + 1} 失败:`, error.message);
      }
    });
    
    await Promise.all(promises);
    console.log('🎉 缓存预热完成');
  },

  // 缓存失效策略
  invalidatePattern: async (redis, pattern) => {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
      console.log(`🗑️ 清除了 ${keys.length} 个缓存键`);
    }
  }
};
```

这些性能优化脚本涵盖了前端、后端、数据库和缓存等各个层面的性能优化需求，可以帮助开发者系统性地提升应用性能。
