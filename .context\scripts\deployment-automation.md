# 部署自动化脚本

## CI/CD 流水线脚本

### GitHub Actions 工作流
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Run linting
        run: npm run lint
      
      - name: Build application
        run: npm run build

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    permissions:
      contents: read
      packages: write
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Log in to Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - name: Deploy to production
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:main
            docker stop myapp || true
            docker rm myapp || true
            docker run -d --name myapp -p 80:3000 \
              -e NODE_ENV=production \
              -e DATABASE_URL=${{ secrets.DATABASE_URL }} \
              ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:main
```

### GitLab CI/CD 配置
```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

before_script:
  - docker info

test:
  stage: test
  image: node:18
  cache:
    paths:
      - node_modules/
  script:
    - npm ci
    - npm run test
    - npm run lint
  artifacts:
    reports:
      junit: junit.xml
      coverage: coverage/
    expire_in: 1 week

build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  only:
    - main
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE:latest
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker push $CI_REGISTRY_IMAGE:latest

deploy_staging:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
  script:
    - ssh -o StrictHostKeyChecking=no $DEPLOY_USER@$STAGING_HOST "
        docker pull $CI_REGISTRY_IMAGE:latest &&
        docker stop myapp-staging || true &&
        docker rm myapp-staging || true &&
        docker run -d --name myapp-staging -p 8080:3000 
          -e NODE_ENV=staging 
          $CI_REGISTRY_IMAGE:latest"
  environment:
    name: staging
    url: http://staging.example.com
  only:
    - main

deploy_production:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
  script:
    - ssh -o StrictHostKeyChecking=no $DEPLOY_USER@$PRODUCTION_HOST "
        docker pull $CI_REGISTRY_IMAGE:latest &&
        docker stop myapp || true &&
        docker rm myapp || true &&
        docker run -d --name myapp -p 80:3000 
          -e NODE_ENV=production 
          -e DATABASE_URL=$DATABASE_URL 
          $CI_REGISTRY_IMAGE:latest"
  environment:
    name: production
    url: http://example.com
  when: manual
  only:
    - main
```

## Docker 部署脚本

### 多阶段构建 Dockerfile
```dockerfile
# 前端应用 Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码并构建
COPY . .
RUN npm run build

# 生产环境镜像
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

```dockerfile
# 后端应用 Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产环境镜像
FROM node:18-alpine

WORKDIR /app

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# 复制构建产物和依赖
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# 设置用户权限
USER nextjs

EXPOSE 3000

ENV NODE_ENV=production

CMD ["node", "dist/index.js"]
```

### Docker Compose 配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************/myapp
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

## Kubernetes 部署脚本

### 应用部署配置
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: myapp

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myapp
  namespace: myapp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: myapp
  template:
    metadata:
      labels:
        app: myapp
    spec:
      containers:
      - name: myapp
        image: myapp:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: myapp-secrets
              key: database-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: myapp-service
  namespace: myapp
spec:
  selector:
    app: myapp
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: myapp-ingress
  namespace: myapp
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - myapp.example.com
    secretName: myapp-tls
  rules:
  - host: myapp.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: myapp-service
            port:
              number: 80
```

### 数据库配置
```yaml
# k8s/postgres.yaml
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: myapp
type: Opaque
data:
  username: dXNlcg== # base64 encoded 'user'
  password: cGFzc3dvcmQ= # base64 encoded 'password'

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: myapp
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: myapp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_DB
          value: myapp
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: myapp
spec:
  selector:
    app: postgres
  ports:
  - protocol: TCP
    port: 5432
    targetPort: 5432
```

## 部署脚本

### 自动化部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

# 配置变量
APP_NAME="myapp"
DOCKER_REGISTRY="ghcr.io/username"
ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}

echo "🚀 开始部署 $APP_NAME 到 $ENVIRONMENT 环境..."

# 检查必要的工具
command -v docker >/dev/null 2>&1 || { echo "❌ Docker 未安装"; exit 1; }
command -v kubectl >/dev/null 2>&1 || { echo "❌ kubectl 未安装"; exit 1; }

# 构建和推送镜像
echo "📦 构建 Docker 镜像..."
docker build -t $DOCKER_REGISTRY/$APP_NAME:$VERSION .
docker push $DOCKER_REGISTRY/$APP_NAME:$VERSION

# 更新 Kubernetes 部署
echo "🔄 更新 Kubernetes 部署..."
kubectl set image deployment/$APP_NAME $APP_NAME=$DOCKER_REGISTRY/$APP_NAME:$VERSION -n $APP_NAME

# 等待部署完成
echo "⏳ 等待部署完成..."
kubectl rollout status deployment/$APP_NAME -n $APP_NAME --timeout=300s

# 验证部署
echo "✅ 验证部署状态..."
kubectl get pods -n $APP_NAME
kubectl get services -n $APP_NAME

# 运行健康检查
echo "🏥 运行健康检查..."
HEALTH_URL="http://$(kubectl get service $APP_NAME-service -n $APP_NAME -o jsonpath='{.status.loadBalancer.ingress[0].ip}')/health"
curl -f $HEALTH_URL || { echo "❌ 健康检查失败"; exit 1; }

echo "🎉 部署完成！"
```

### 回滚脚本
```bash
#!/bin/bash
# rollback.sh

set -e

APP_NAME="myapp"
ENVIRONMENT=${1:-staging}

echo "🔄 开始回滚 $APP_NAME 在 $ENVIRONMENT 环境..."

# 获取上一个版本
PREVIOUS_REVISION=$(kubectl rollout history deployment/$APP_NAME -n $APP_NAME | tail -2 | head -1 | awk '{print $1}')

if [ -z "$PREVIOUS_REVISION" ]; then
    echo "❌ 没有找到可回滚的版本"
    exit 1
fi

echo "📋 回滚到版本: $PREVIOUS_REVISION"

# 执行回滚
kubectl rollout undo deployment/$APP_NAME -n $APP_NAME --to-revision=$PREVIOUS_REVISION

# 等待回滚完成
kubectl rollout status deployment/$APP_NAME -n $APP_NAME --timeout=300s

echo "✅ 回滚完成！"
```

### 数据库迁移脚本
```bash
#!/bin/bash
# migrate.sh

set -e

APP_NAME="myapp"
ENVIRONMENT=${1:-staging}

echo "🗄️ 开始数据库迁移..."

# 运行迁移任务
kubectl run migration-job --image=$DOCKER_REGISTRY/$APP_NAME:latest \
  --restart=Never \
  --rm -i --tty \
  --env="NODE_ENV=$ENVIRONMENT" \
  --env="DATABASE_URL=$(kubectl get secret myapp-secrets -n $APP_NAME -o jsonpath='{.data.database-url}' | base64 -d)" \
  --command -- npm run migrate

echo "✅ 数据库迁移完成！"
```

这些部署自动化脚本涵盖了从CI/CD流水线到容器化部署的完整流程，可以帮助团队实现高效、可靠的自动化部署。
