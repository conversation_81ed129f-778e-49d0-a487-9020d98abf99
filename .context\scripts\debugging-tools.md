# 调试工具脚本

## 前端调试脚本

### React 调试工具
```javascript
// React DevTools 调试助手
const ReactDebugger = {
  // 查找组件实例
  findComponent: (componentName) => {
    const allComponents = document.querySelectorAll('[data-reactroot] *');
    return Array.from(allComponents).filter(el => {
      const fiber = el._reactInternalFiber || el._reactInternalInstance;
      return fiber && fiber.type && fiber.type.name === componentName;
    });
  },

  // 获取组件props
  getProps: (element) => {
    const fiber = element._reactInternalFiber || element._reactInternalInstance;
    return fiber ? fiber.memoizedProps : null;
  },

  // 获取组件state
  getState: (element) => {
    const fiber = element._reactInternalFiber || element._reactInternalInstance;
    return fiber ? fiber.memoizedState : null;
  },

  // 强制重新渲染
  forceUpdate: (element) => {
    const fiber = element._reactInternalFiber || element._reactInternalInstance;
    if (fiber && fiber.stateNode && fiber.stateNode.forceUpdate) {
      fiber.stateNode.forceUpdate();
    }
  }
};

// 性能监控
const PerformanceMonitor = {
  // 监控渲染时间
  measureRender: (componentName, renderFn) => {
    const start = performance.now();
    const result = renderFn();
    const end = performance.now();
    console.log(`${componentName} render time: ${end - start}ms`);
    return result;
  },

  // 内存使用监控
  checkMemory: () => {
    if (performance.memory) {
      console.log({
        used: Math.round(performance.memory.usedJSHeapSize / 1048576) + 'MB',
        total: Math.round(performance.memory.totalJSHeapSize / 1048576) + 'MB',
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) + 'MB'
      });
    }
  },

  // 长任务监控
  monitorLongTasks: () => {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          console.warn(`Long task detected: ${entry.duration}ms`, entry);
        });
      });
      observer.observe({ entryTypes: ['longtask'] });
    }
  }
};

// 网络请求调试
const NetworkDebugger = {
  // 拦截fetch请求
  interceptFetch: () => {
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
      console.log('Fetch request:', args);
      return originalFetch.apply(this, args)
        .then(response => {
          console.log('Fetch response:', response);
          return response;
        })
        .catch(error => {
          console.error('Fetch error:', error);
          throw error;
        });
    };
  },

  // 拦截XMLHttpRequest
  interceptXHR: () => {
    const originalOpen = XMLHttpRequest.prototype.open;
    const originalSend = XMLHttpRequest.prototype.send;

    XMLHttpRequest.prototype.open = function(method, url) {
      this._method = method;
      this._url = url;
      return originalOpen.apply(this, arguments);
    };

    XMLHttpRequest.prototype.send = function(data) {
      console.log(`XHR ${this._method} ${this._url}`, data);
      
      this.addEventListener('load', function() {
        console.log(`XHR Response ${this._method} ${this._url}:`, this.responseText);
      });

      return originalSend.apply(this, arguments);
    };
  }
};
```

### Vue 调试工具
```javascript
// Vue DevTools 调试助手
const VueDebugger = {
  // 获取Vue实例
  getInstance: (element) => {
    return element.__vue__ || element.__vueParentComponent;
  },

  // 获取组件数据
  getData: (element) => {
    const instance = VueDebugger.getInstance(element);
    return instance ? instance.$data || instance.data : null;
  },

  // 获取组件props
  getProps: (element) => {
    const instance = VueDebugger.getInstance(element);
    return instance ? instance.$props || instance.props : null;
  },

  // 触发事件
  emit: (element, eventName, payload) => {
    const instance = VueDebugger.getInstance(element);
    if (instance && instance.$emit) {
      instance.$emit(eventName, payload);
    }
  }
};

// Vue 3 Composition API 调试
const Vue3Debugger = {
  // 调试响应式数据
  debugReactive: (obj) => {
    return new Proxy(obj, {
      get(target, prop) {
        console.log(`Getting ${prop}:`, target[prop]);
        return target[prop];
      },
      set(target, prop, value) {
        console.log(`Setting ${prop}:`, value);
        target[prop] = value;
        return true;
      }
    });
  },

  // 监控computed变化
  watchComputed: (computedRef, label) => {
    const { watchEffect } = Vue;
    watchEffect(() => {
      console.log(`${label} computed changed:`, computedRef.value);
    });
  }
};
```

## 后端调试脚本

### Node.js 调试工具
```javascript
// 请求日志中间件
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url} - ${res.statusCode} (${duration}ms)`);
  });
  
  next();
};

// 错误处理中间件
const errorHandler = (err, req, res, next) => {
  console.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    params: req.params,
    query: req.query
  });
  
  res.status(500).json({
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal Server Error',
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
};

// 内存监控
const memoryMonitor = {
  start: () => {
    setInterval(() => {
      const usage = process.memoryUsage();
      console.log('Memory Usage:', {
        rss: Math.round(usage.rss / 1024 / 1024) + 'MB',
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024) + 'MB',
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + 'MB',
        external: Math.round(usage.external / 1024 / 1024) + 'MB'
      });
    }, 30000); // 每30秒检查一次
  }
};

// 数据库查询调试
const dbDebugger = {
  // MongoDB查询调试
  mongooseDebug: () => {
    const mongoose = require('mongoose');
    mongoose.set('debug', (collectionName, method, query, doc) => {
      console.log(`MongoDB ${method} on ${collectionName}:`, {
        query,
        doc: doc ? JSON.stringify(doc).substring(0, 100) + '...' : undefined
      });
    });
  },

  // SQL查询调试 (以Sequelize为例)
  sequelizeDebug: () => {
    const { Sequelize } = require('sequelize');
    const sequelize = new Sequelize(process.env.DATABASE_URL, {
      logging: (sql, timing) => {
        console.log(`SQL Query (${timing}ms):`, sql);
      }
    });
    return sequelize;
  }
};
```

### Python 调试工具
```python
# FastAPI调试中间件
import time
import logging
from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware

class DebugMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # 记录请求信息
        logging.info(f"Request: {request.method} {request.url}")
        logging.info(f"Headers: {dict(request.headers)}")
        
        response = await call_next(request)
        
        # 记录响应信息
        process_time = time.time() - start_time
        logging.info(f"Response: {response.status_code} ({process_time:.3f}s)")
        
        return response

# 数据库查询调试
import sqlalchemy
from sqlalchemy import event
from sqlalchemy.engine import Engine

@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    context._query_start_time = time.time()
    logging.info(f"SQL Query: {statement}")
    logging.info(f"Parameters: {parameters}")

@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total = time.time() - context._query_start_time
    logging.info(f"Query completed in {total:.3f}s")

# 内存监控
import psutil
import threading

def monitor_memory():
    def check_memory():
        while True:
            process = psutil.Process()
            memory_info = process.memory_info()
            logging.info(f"Memory Usage: {memory_info.rss / 1024 / 1024:.2f}MB")
            time.sleep(30)
    
    thread = threading.Thread(target=check_memory, daemon=True)
    thread.start()

# 异常捕获装饰器
import functools
import traceback

def debug_exceptions(func):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logging.error(f"Exception in {func.__name__}: {str(e)}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            raise
    return wrapper
```

## 数据库调试脚本

### SQL 调试查询
```sql
-- 查询执行计划
EXPLAIN ANALYZE SELECT * FROM users WHERE email = '<EMAIL>';

-- 查看慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- 查看数据库连接
SELECT pid, usename, application_name, client_addr, state, query
FROM pg_stat_activity
WHERE state = 'active';

-- 查看表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 查看索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

### MongoDB 调试查询
```javascript
// 查询执行计划
db.users.find({email: "<EMAIL>"}).explain("executionStats");

// 查看慢查询
db.setProfilingLevel(2, {slowms: 100});
db.system.profile.find().sort({ts: -1}).limit(5);

// 查看索引使用情况
db.users.getIndexes();
db.users.totalIndexSize();

// 查看集合统计信息
db.users.stats();

// 查看当前操作
db.currentOp();
```

## 性能分析脚本

### 前端性能分析
```javascript
// Core Web Vitals 监控
const measureWebVitals = () => {
  // Largest Contentful Paint
  new PerformanceObserver((entryList) => {
    const entries = entryList.getEntries();
    const lastEntry = entries[entries.length - 1];
    console.log('LCP:', lastEntry.startTime);
  }).observe({entryTypes: ['largest-contentful-paint']});

  // First Input Delay
  new PerformanceObserver((entryList) => {
    const firstInput = entryList.getEntries()[0];
    console.log('FID:', firstInput.processingStart - firstInput.startTime);
  }).observe({entryTypes: ['first-input']});

  // Cumulative Layout Shift
  let clsValue = 0;
  new PerformanceObserver((entryList) => {
    for (const entry of entryList.getEntries()) {
      if (!entry.hadRecentInput) {
        clsValue += entry.value;
        console.log('CLS:', clsValue);
      }
    }
  }).observe({entryTypes: ['layout-shift']});
};

// 资源加载分析
const analyzeResources = () => {
  const resources = performance.getEntriesByType('resource');
  const analysis = resources.map(resource => ({
    name: resource.name,
    type: resource.initiatorType,
    size: resource.transferSize,
    duration: resource.duration,
    startTime: resource.startTime
  }));
  
  console.table(analysis.sort((a, b) => b.duration - a.duration));
};
```

这些调试脚本涵盖了前端、后端、数据库等各个层面的调试需求，可以帮助开发者快速定位和解决问题。
