# 项目初始化脚本

## 前端项目初始化

### React + TypeScript 项目
```bash
# 创建React项目
npx create-react-app my-app --template typescript
cd my-app

# 安装常用依赖
npm install @types/node @types/react @types/react-dom
npm install -D eslint prettier @typescript-eslint/eslint-plugin @typescript-eslint/parser

# 安装UI库 (可选)
npm install @mui/material @emotion/react @emotion/styled
# 或者
npm install antd

# 安装路由
npm install react-router-dom @types/react-router-dom

# 安装状态管理
npm install @reduxjs/toolkit react-redux
# 或者
npm install zustand

# 启动开发服务器
npm start
```

### Vue 3 + TypeScript 项目
```bash
# 创建Vue项目
npm create vue@latest my-vue-app
cd my-vue-app

# 选择配置：
# ✅ TypeScript
# ✅ Router
# ✅ Pinia (状态管理)
# ✅ ESLint
# ✅ Prettier

# 安装依赖
npm install

# 安装UI库 (可选)
npm install element-plus
# 或者
npm install naive-ui

# 启动开发服务器
npm run dev
```

### Next.js 项目
```bash
# 创建Next.js项目
npx create-next-app@latest my-next-app --typescript --tailwind --eslint --app
cd my-next-app

# 安装常用依赖
npm install @next/font lucide-react

# 安装UI库 (可选)
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu
npm install class-variance-authority clsx tailwind-merge

# 启动开发服务器
npm run dev
```

## 后端项目初始化

### Node.js + Express + TypeScript
```bash
# 创建项目目录
mkdir my-api && cd my-api
npm init -y

# 安装依赖
npm install express cors helmet morgan dotenv
npm install -D typescript @types/node @types/express @types/cors nodemon ts-node

# 安装数据库相关
npm install mongoose # MongoDB
# 或者
npm install pg @types/pg # PostgreSQL
# 或者
npm install mysql2 # MySQL

# 创建TypeScript配置
npx tsc --init

# 创建基础目录结构
mkdir src src/routes src/controllers src/models src/middleware src/utils

# 启动脚本 (package.json)
# "scripts": {
#   "dev": "nodemon src/index.ts",
#   "build": "tsc",
#   "start": "node dist/index.js"
# }
```

### Python + FastAPI 项目
```bash
# 创建项目目录
mkdir my-fastapi && cd my-fastapi

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 安装依赖
pip install fastapi uvicorn python-multipart
pip install sqlalchemy alembic  # 数据库ORM
pip install pydantic-settings    # 配置管理
pip install pytest pytest-asyncio  # 测试

# 创建requirements.txt
pip freeze > requirements.txt

# 创建基础目录结构
mkdir app app/api app/core app/models app/schemas app/crud

# 启动服务器
uvicorn app.main:app --reload
```

### Go + Gin 项目
```bash
# 创建项目目录
mkdir my-go-api && cd my-go-api

# 初始化Go模块
go mod init my-go-api

# 安装依赖
go get github.com/gin-gonic/gin
go get github.com/joho/godotenv
go get gorm.io/gorm
go get gorm.io/driver/postgres  # 或其他数据库驱动

# 创建基础目录结构
mkdir cmd internal pkg
mkdir internal/handler internal/service internal/repository internal/model

# 创建main.go
# package main
# 
# import "github.com/gin-gonic/gin"
# 
# func main() {
#     r := gin.Default()
#     r.GET("/", func(c *gin.Context) {
#         c.JSON(200, gin.H{"message": "Hello World"})
#     })
#     r.Run(":8080")
# }
```

## 移动端项目初始化

### React Native 项目
```bash
# 安装React Native CLI
npm install -g @react-native-community/cli

# 创建项目
npx react-native init MyApp --template react-native-template-typescript
cd MyApp

# 安装导航库
npm install @react-navigation/native @react-navigation/stack @react-navigation/bottom-tabs
npm install react-native-screens react-native-safe-area-context

# 安装状态管理
npm install @reduxjs/toolkit react-redux

# 安装UI库
npm install react-native-elements react-native-vector-icons
# 或者
npm install native-base react-native-svg

# iOS运行
npx react-native run-ios

# Android运行
npx react-native run-android
```

### Flutter 项目
```bash
# 创建Flutter项目
flutter create my_flutter_app
cd my_flutter_app

# 添加常用依赖到pubspec.yaml
# dependencies:
#   http: ^0.13.5
#   provider: ^6.0.5
#   shared_preferences: ^2.0.18
#   cached_network_image: ^3.2.3

# 安装依赖
flutter pub get

# 运行项目
flutter run
```

## 全栈项目初始化

### MEAN Stack (MongoDB + Express + Angular + Node.js)
```bash
# 安装Angular CLI
npm install -g @angular/cli

# 创建项目结构
mkdir mean-app && cd mean-app
mkdir backend frontend

# 后端设置
cd backend
npm init -y
npm install express mongoose cors dotenv bcryptjs jsonwebtoken
npm install -D nodemon

# 前端设置
cd ../frontend
ng new frontend --routing --style=scss
cd frontend
ng add @angular/material

# 安装HTTP客户端
npm install @angular/common
```

### MERN Stack (MongoDB + Express + React + Node.js)
```bash
# 创建项目结构
mkdir mern-app && cd mern-app
mkdir backend frontend

# 后端设置
cd backend
npm init -y
npm install express mongoose cors dotenv bcryptjs jsonwebtoken
npm install -D nodemon concurrently

# 前端设置
cd ../frontend
npx create-react-app . --template typescript
npm install axios react-router-dom @types/react-router-dom

# 根目录package.json脚本
# "scripts": {
#   "dev": "concurrently \"npm run server\" \"npm run client\"",
#   "server": "cd backend && npm run dev",
#   "client": "cd frontend && npm start"
# }
```

## 开发工具配置

### ESLint + Prettier 配置
```bash
# 安装ESLint和Prettier
npm install -D eslint prettier eslint-config-prettier eslint-plugin-prettier

# 创建.eslintrc.js
# module.exports = {
#   extends: ['eslint:recommended', 'prettier'],
#   plugins: ['prettier'],
#   rules: {
#     'prettier/prettier': 'error'
#   }
# }

# 创建.prettierrc
# {
#   "semi": true,
#   "trailingComma": "es5",
#   "singleQuote": true,
#   "printWidth": 80,
#   "tabWidth": 2
# }
```

### Git配置
```bash
# 初始化Git仓库
git init

# 创建.gitignore
echo "node_modules/
.env
dist/
build/
*.log" > .gitignore

# 设置Git hooks (可选)
npm install -D husky lint-staged
npx husky install
npx husky add .husky/pre-commit "npx lint-staged"

# package.json中添加
# "lint-staged": {
#   "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]
# }
```

### Docker配置
```dockerfile
# Dockerfile示例 (Node.js)
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
```

```yaml
# docker-compose.yml示例
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - db
  
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 测试环境配置

### Jest + Testing Library
```bash
# React项目测试
npm install -D @testing-library/react @testing-library/jest-dom @testing-library/user-event

# Node.js项目测试
npm install -D jest @types/jest supertest @types/supertest

# 创建jest.config.js
# module.exports = {
#   testEnvironment: 'node',
#   collectCoverageFrom: [
#     'src/**/*.{js,ts}',
#     '!src/**/*.d.ts'
#   ],
#   coverageThreshold: {
#     global: {
#       branches: 80,
#       functions: 80,
#       lines: 80,
#       statements: 80
#     }
#   }
# }
```

这些脚本涵盖了常见的项目初始化场景，可以帮助开发者快速搭建各种类型的项目。
