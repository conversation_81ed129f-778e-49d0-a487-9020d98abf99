# 测试自动化脚本

## 前端测试脚本

### React 测试工具
```javascript
// React Testing Library 测试工具
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { jest } from '@jest/globals';

// 测试工具类
export class ReactTestUtils {
  // 渲染组件并提供常用查询
  static renderWithProviders(component, options = {}) {
    const {
      initialState = {},
      store = createTestStore(initialState),
      ...renderOptions
    } = options;

    const Wrapper = ({ children }) => (
      <Provider store={store}>
        <BrowserRouter>
          <ThemeProvider theme={defaultTheme}>
            {children}
          </ThemeProvider>
        </BrowserRouter>
      </Provider>
    );

    return {
      ...render(component, { wrapper: Wrapper, ...renderOptions }),
      store
    };
  }

  // 模拟API响应
  static mockApiResponse(url, response, status = 200) {
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: status >= 200 && status < 300,
        status,
        json: () => Promise.resolve(response),
        text: () => Promise.resolve(JSON.stringify(response))
      })
    );
  }

  // 等待异步操作完成
  static async waitForAsyncOperations() {
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  }

  // 模拟用户交互
  static async simulateUserInteraction(element, action, value) {
    const user = userEvent.setup();
    
    switch (action) {
      case 'click':
        await user.click(element);
        break;
      case 'type':
        await user.type(element, value);
        break;
      case 'clear':
        await user.clear(element);
        break;
      case 'selectOptions':
        await user.selectOptions(element, value);
        break;
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  }

  // 验证表单验证
  static async testFormValidation(formElement, testCases) {
    for (const testCase of testCases) {
      const { field, value, expectedError } = testCase;
      
      const input = screen.getByLabelText(field);
      await this.simulateUserInteraction(input, 'clear');
      await this.simulateUserInteraction(input, 'type', value);
      
      fireEvent.blur(input);
      
      if (expectedError) {
        await waitFor(() => {
          expect(screen.getByText(expectedError)).toBeInTheDocument();
        });
      } else {
        await waitFor(() => {
          expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
        });
      }
    }
  }

  // 测试组件可访问性
  static async testAccessibility(component) {
    const { container } = this.renderWithProviders(component);
    
    // 检查是否有适当的ARIA标签
    const buttons = container.querySelectorAll('button');
    buttons.forEach(button => {
      expect(button).toHaveAttribute('aria-label');
    });

    // 检查表单元素是否有标签
    const inputs = container.querySelectorAll('input');
    inputs.forEach(input => {
      const label = container.querySelector(`label[for="${input.id}"]`);
      expect(label).toBeInTheDocument();
    });

    // 检查图片是否有alt属性
    const images = container.querySelectorAll('img');
    images.forEach(img => {
      expect(img).toHaveAttribute('alt');
    });
  }
}

// 自定义测试钩子
export const useTestHooks = () => {
  // 模拟localStorage
  const mockLocalStorage = () => {
    const store = {};
    return {
      getItem: jest.fn(key => store[key] || null),
      setItem: jest.fn((key, value) => { store[key] = value; }),
      removeItem: jest.fn(key => { delete store[key]; }),
      clear: jest.fn(() => { Object.keys(store).forEach(key => delete store[key]); })
    };
  };

  // 模拟IntersectionObserver
  const mockIntersectionObserver = () => {
    global.IntersectionObserver = jest.fn().mockImplementation((callback) => ({
      observe: jest.fn(),
      unobserve: jest.fn(),
      disconnect: jest.fn(),
      trigger: (entries) => callback(entries)
    }));
  };

  // 模拟ResizeObserver
  const mockResizeObserver = () => {
    global.ResizeObserver = jest.fn().mockImplementation((callback) => ({
      observe: jest.fn(),
      unobserve: jest.fn(),
      disconnect: jest.fn(),
      trigger: (entries) => callback(entries)
    }));
  };

  return {
    mockLocalStorage,
    mockIntersectionObserver,
    mockResizeObserver
  };
};

// 性能测试工具
export const PerformanceTestUtils = {
  // 测试组件渲染性能
  measureRenderTime: async (component, iterations = 100) => {
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      const { unmount } = ReactTestUtils.renderWithProviders(component);
      const end = performance.now();
      
      times.push(end - start);
      unmount();
    }
    
    const average = times.reduce((sum, time) => sum + time, 0) / times.length;
    const max = Math.max(...times);
    const min = Math.min(...times);
    
    return { average, max, min, times };
  },

  // 测试内存泄漏
  detectMemoryLeaks: async (component, cycles = 10) => {
    const initialMemory = performance.memory?.usedJSHeapSize || 0;
    
    for (let i = 0; i < cycles; i++) {
      const { unmount } = ReactTestUtils.renderWithProviders(component);
      unmount();
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
    }
    
    const finalMemory = performance.memory?.usedJSHeapSize || 0;
    const memoryIncrease = finalMemory - initialMemory;
    
    return {
      initialMemory,
      finalMemory,
      memoryIncrease,
      hasLeak: memoryIncrease > 1024 * 1024 // 1MB threshold
    };
  }
};
```

### Vue 测试工具
```javascript
// Vue Test Utils 测试工具
import { mount, shallowMount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { vi } from 'vitest';

export class VueTestUtils {
  // 创建测试包装器
  static createWrapper(component, options = {}) {
    const {
      props = {},
      global = {},
      shallow = false,
      ...mountOptions
    } = options;

    const defaultGlobal = {
      plugins: [createTestingPinia({ createSpy: vi.fn })],
      stubs: {
        'router-link': true,
        'router-view': true
      },
      ...global
    };

    const mountFunction = shallow ? shallowMount : mount;
    
    return mountFunction(component, {
      props,
      global: defaultGlobal,
      ...mountOptions
    });
  }

  // 模拟组合式API
  static mockComposable(composableName, mockImplementation) {
    vi.mock(composableName, () => ({
      default: mockImplementation,
      [composableName]: mockImplementation
    }));
  }

  // 测试事件发射
  static async testEmittedEvents(wrapper, eventName, expectedPayload) {
    await wrapper.vm.$nextTick();
    
    const emittedEvents = wrapper.emitted(eventName);
    expect(emittedEvents).toBeTruthy();
    
    if (expectedPayload !== undefined) {
      expect(emittedEvents[0][0]).toEqual(expectedPayload);
    }
  }

  // 测试插槽内容
  static testSlotContent(wrapper, slotName, expectedContent) {
    const slotContent = wrapper.find(`[data-testid="${slotName}-slot"]`);
    expect(slotContent.text()).toContain(expectedContent);
  }

  // 模拟路由
  static mockRouter(routes = []) {
    return {
      push: vi.fn(),
      replace: vi.fn(),
      go: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      currentRoute: {
        value: {
          path: '/',
          params: {},
          query: {},
          ...routes[0]
        }
      }
    };
  }
}

// Vue 3 Composition API 测试工具
export const CompositionTestUtils = {
  // 测试响应式数据
  testReactivity: (composable) => {
    const { result, rerender } = renderComposable(composable);
    
    // 测试初始值
    expect(result.current.value).toBeDefined();
    
    // 测试响应式更新
    act(() => {
      result.current.setValue('new value');
    });
    
    expect(result.current.value).toBe('new value');
  },

  // 测试计算属性
  testComputed: (composable, dependencies) => {
    const { result } = renderComposable(composable);
    
    dependencies.forEach(({ prop, value, expectedComputed }) => {
      act(() => {
        result.current[prop].value = value;
      });
      
      expect(result.current.computed.value).toBe(expectedComputed);
    });
  },

  // 测试生命周期钩子
  testLifecycleHooks: (composable) => {
    const onMountedSpy = vi.fn();
    const onUnmountedSpy = vi.fn();
    
    const { unmount } = renderComposable(() => {
      onMounted(onMountedSpy);
      onUnmounted(onUnmountedSpy);
      return composable();
    });
    
    expect(onMountedSpy).toHaveBeenCalled();
    
    unmount();
    expect(onUnmountedSpy).toHaveBeenCalled();
  }
};
```

## 后端测试脚本

### Node.js/Express 测试工具
```javascript
// Express API 测试工具
import request from 'supertest';
import { jest } from '@jest/globals';

export class APITestUtils {
  constructor(app) {
    this.app = app;
    this.agent = request.agent(app);
  }

  // 认证测试用户
  async authenticateUser(credentials) {
    const response = await this.agent
      .post('/api/auth/login')
      .send(credentials)
      .expect(200);
    
    this.authToken = response.body.token;
    return this.authToken;
  }

  // 发送认证请求
  authenticatedRequest(method, url) {
    const req = this.agent[method.toLowerCase()](url);
    
    if (this.authToken) {
      req.set('Authorization', `Bearer ${this.authToken}`);
    }
    
    return req;
  }

  // 测试API端点
  async testEndpoint(config) {
    const {
      method = 'GET',
      url,
      data,
      expectedStatus = 200,
      expectedFields = [],
      authenticated = false
    } = config;

    let request = authenticated 
      ? this.authenticatedRequest(method, url)
      : this.agent[method.toLowerCase()](url);

    if (data) {
      request = request.send(data);
    }

    const response = await request.expect(expectedStatus);

    // 验证响应字段
    expectedFields.forEach(field => {
      expect(response.body).toHaveProperty(field);
    });

    return response;
  }

  // 测试分页
  async testPagination(url, options = {}) {
    const { pageSize = 10, totalPages = 3 } = options;
    
    for (let page = 1; page <= totalPages; page++) {
      const response = await this.agent
        .get(`${url}?page=${page}&limit=${pageSize}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.pagination.page).toBe(page);
      expect(response.body.data.length).toBeLessThanOrEqual(pageSize);
    }
  }

  // 测试输入验证
  async testValidation(url, validationTests) {
    for (const test of validationTests) {
      const { data, expectedErrors } = test;
      
      const response = await this.agent
        .post(url)
        .send(data)
        .expect(400);

      expectedErrors.forEach(error => {
        expect(response.body.errors).toContain(error);
      });
    }
  }

  // 测试错误处理
  async testErrorHandling(url, errorScenarios) {
    for (const scenario of errorScenarios) {
      const { setup, expectedStatus, expectedMessage } = scenario;
      
      if (setup) {
        await setup();
      }
      
      const response = await this.agent
        .get(url)
        .expect(expectedStatus);

      if (expectedMessage) {
        expect(response.body.message).toContain(expectedMessage);
      }
    }
  }
}

// 数据库测试工具
export class DatabaseTestUtils {
  constructor(db) {
    this.db = db;
  }

  // 创建测试数据
  async createTestData(model, data) {
    if (Array.isArray(data)) {
      return await model.insertMany(data);
    } else {
      return await model.create(data);
    }
  }

  // 清理测试数据
  async cleanupTestData(models) {
    for (const model of models) {
      await model.deleteMany({});
    }
  }

  // 测试数据库操作
  async testDatabaseOperation(operation, expectedResult) {
    const result = await operation();
    
    if (expectedResult) {
      expect(result).toMatchObject(expectedResult);
    }
    
    return result;
  }

  // 测试事务
  async testTransaction(operations) {
    const session = await this.db.startSession();
    
    try {
      await session.withTransaction(async () => {
        for (const operation of operations) {
          await operation(session);
        }
      });
    } finally {
      await session.endSession();
    }
  }

  // 性能测试
  async testQueryPerformance(query, maxExecutionTime = 1000) {
    const start = Date.now();
    await query();
    const executionTime = Date.now() - start;
    
    expect(executionTime).toBeLessThan(maxExecutionTime);
    return executionTime;
  }
}

// Mock 工具
export const MockUtils = {
  // 模拟外部API
  mockExternalAPI: (baseURL, responses) => {
    const originalFetch = global.fetch;
    
    global.fetch = jest.fn((url, options) => {
      const endpoint = url.replace(baseURL, '');
      const method = options?.method || 'GET';
      const key = `${method} ${endpoint}`;
      
      if (responses[key]) {
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve(responses[key])
        });
      }
      
      return originalFetch(url, options);
    });
    
    return () => {
      global.fetch = originalFetch;
    };
  },

  // 模拟数据库
  mockDatabase: (model, mockData) => {
    const originalMethods = {};
    
    ['find', 'findOne', 'create', 'updateOne', 'deleteOne'].forEach(method => {
      originalMethods[method] = model[method];
      
      model[method] = jest.fn().mockImplementation(() => {
        switch (method) {
          case 'find':
            return Promise.resolve(mockData);
          case 'findOne':
            return Promise.resolve(mockData[0]);
          case 'create':
            return Promise.resolve(mockData[0]);
          default:
            return Promise.resolve({ acknowledged: true });
        }
      });
    });
    
    return () => {
      Object.keys(originalMethods).forEach(method => {
        model[method] = originalMethods[method];
      });
    };
  },

  // 模拟邮件服务
  mockEmailService: () => {
    const emailsSent = [];
    
    return {
      sendEmail: jest.fn((to, subject, body) => {
        emailsSent.push({ to, subject, body, sentAt: new Date() });
        return Promise.resolve({ messageId: 'mock-message-id' });
      }),
      getEmailsSent: () => emailsSent,
      clearEmails: () => emailsSent.length = 0
    };
  }
};
```

## 端到端测试脚本

### Playwright 测试工具
```javascript
// Playwright E2E 测试工具
import { test, expect, Page } from '@playwright/test';

export class E2ETestUtils {
  constructor(page) {
    this.page = page;
  }

  // 登录用户
  async loginUser(credentials) {
    await this.page.goto('/login');
    await this.page.fill('[data-testid="email"]', credentials.email);
    await this.page.fill('[data-testid="password"]', credentials.password);
    await this.page.click('[data-testid="login-button"]');
    await this.page.waitForURL('/dashboard');
  }

  // 等待元素出现
  async waitForElement(selector, options = {}) {
    return await this.page.waitForSelector(selector, {
      timeout: 10000,
      ...options
    });
  }

  // 截图对比
  async compareScreenshot(name, options = {}) {
    await expect(this.page).toHaveScreenshot(`${name}.png`, {
      fullPage: true,
      ...options
    });
  }

  // 测试表单提交
  async testFormSubmission(formData, expectedResult) {
    // 填写表单
    for (const [field, value] of Object.entries(formData)) {
      await this.page.fill(`[data-testid="${field}"]`, value);
    }
    
    // 提交表单
    await this.page.click('[data-testid="submit-button"]');
    
    // 验证结果
    if (expectedResult.success) {
      await expect(this.page.locator('[data-testid="success-message"]'))
        .toBeVisible();
    } else {
      await expect(this.page.locator('[data-testid="error-message"]'))
        .toContainText(expectedResult.error);
    }
  }

  // 测试导航
  async testNavigation(navigationTests) {
    for (const test of navigationTests) {
      const { trigger, expectedURL, expectedTitle } = test;
      
      if (trigger.type === 'click') {
        await this.page.click(trigger.selector);
      } else if (trigger.type === 'goto') {
        await this.page.goto(trigger.url);
      }
      
      await this.page.waitForURL(expectedURL);
      
      if (expectedTitle) {
        await expect(this.page).toHaveTitle(expectedTitle);
      }
    }
  }

  // 性能测试
  async testPagePerformance(url, thresholds = {}) {
    const response = await this.page.goto(url);
    
    // 测试页面加载时间
    const loadTime = await this.page.evaluate(() => {
      return performance.timing.loadEventEnd - performance.timing.navigationStart;
    });
    
    if (thresholds.loadTime) {
      expect(loadTime).toBeLessThan(thresholds.loadTime);
    }
    
    // 测试Core Web Vitals
    const vitals = await this.page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals = {};
        
        // LCP
        new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          vitals.lcp = entries[entries.length - 1].startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // FID
        new PerformanceObserver((entryList) => {
          const firstInput = entryList.getEntries()[0];
          vitals.fid = firstInput.processingStart - firstInput.startTime;
        }).observe({ entryTypes: ['first-input'] });
        
        setTimeout(() => resolve(vitals), 3000);
      });
    });
    
    return { loadTime, vitals };
  }

  // 可访问性测试
  async testAccessibility() {
    const accessibilityResults = await this.page.evaluate(() => {
      // 检查是否有alt属性
      const imagesWithoutAlt = Array.from(document.querySelectorAll('img'))
        .filter(img => !img.alt);
      
      // 检查是否有适当的标题结构
      const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
      
      // 检查表单标签
      const inputsWithoutLabels = Array.from(document.querySelectorAll('input'))
        .filter(input => !document.querySelector(`label[for="${input.id}"]`));
      
      return {
        imagesWithoutAlt: imagesWithoutAlt.length,
        headingsCount: headings.length,
        inputsWithoutLabels: inputsWithoutLabels.length
      };
    });
    
    expect(accessibilityResults.imagesWithoutAlt).toBe(0);
    expect(accessibilityResults.inputsWithoutLabels).toBe(0);
    
    return accessibilityResults;
  }
}

// 测试数据生成器
export const TestDataGenerator = {
  // 生成用户数据
  generateUser: (overrides = {}) => ({
    id: Math.random().toString(36).substr(2, 9),
    name: 'Test User',
    email: `test${Date.now()}@example.com`,
    password: 'password123',
    createdAt: new Date().toISOString(),
    ...overrides
  }),

  // 生成产品数据
  generateProduct: (overrides = {}) => ({
    id: Math.random().toString(36).substr(2, 9),
    name: 'Test Product',
    price: 99.99,
    description: 'Test product description',
    category: 'electronics',
    inStock: true,
    ...overrides
  }),

  // 生成订单数据
  generateOrder: (overrides = {}) => ({
    id: Math.random().toString(36).substr(2, 9),
    userId: 'user123',
    items: [TestDataGenerator.generateProduct()],
    total: 99.99,
    status: 'pending',
    createdAt: new Date().toISOString(),
    ...overrides
  }),

  // 批量生成数据
  generateBatch: (generator, count, overrides = {}) => {
    return Array.from({ length: count }, (_, index) => 
      generator({ ...overrides, index })
    );
  }
};
```

这些测试自动化脚本涵盖了前端、后端和端到端测试的各个方面，提供了完整的测试工具链，可以帮助开发团队建立高质量的自动化测试体系。
